/**
 * 3D Solar System Simulation
 * Built with Three.js
 */

class SolarSystem {
    constructor() {
        // Core Three.js components
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.clock = new THREE.Clock();
        
        // Solar system objects
        this.sun = null;
        this.planets = [];
        this.stars = [];
        
        // Animation and control state
        this.isPaused = false;
        this.globalSpeedMultiplier = 1;
        this.planetSpeedMultipliers = {};
        
        // UI elements
        this.canvas = null;
        this.loadingScreen = null;
        this.planetInfo = null;
        
        // Mouse interaction
        this.mouse = new THREE.Vector2();
        this.raycaster = new THREE.Raycaster();
        this.hoveredPlanet = null;
        
        // Initialize the application
        this.init();
    }
    
    /**
     * Initialize the solar system simulation
     */
    async init() {
        try {
            this.setupDOM();
            this.setupScene();
            this.setupCamera();
            this.setupRenderer();
            this.setupLighting();
            this.setupPlanetData();
            await this.createSolarSystem();
            this.setupControls();
            this.setupEventListeners();
            this.startAnimation();
            this.hideLoadingScreen();
        } catch (error) {
            console.error('Failed to initialize solar system:', error);
        }
    }
    
    /**
     * Setup DOM references
     */
    setupDOM() {
        this.canvas = document.getElementById('solarSystemCanvas');
        this.loadingScreen = document.getElementById('loadingScreen');
        this.planetInfo = document.getElementById('planetInfo');
    }
    
    /**
     * Setup Three.js scene
     */
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000011);
    }
    
    /**
     * Setup camera with proper positioning
     */
    setupCamera() {
        const aspect = window.innerWidth / window.innerHeight;
        this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 10000);
        this.camera.position.set(0, 50, 100);
        this.camera.lookAt(0, 0, 0);
    }
    
    /**
     * Setup WebGL renderer
     */
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }
    
    /**
     * Setup lighting system
     */
    setupLighting() {
        // Ambient light for general illumination
        const ambientLight = new THREE.AmbientLight(0x404040, 0.2);
        this.scene.add(ambientLight);
        
        // Point light from the sun
        const sunLight = new THREE.PointLight(0xffffff, 2, 1000);
        sunLight.position.set(0, 0, 0);
        sunLight.castShadow = true;
        sunLight.shadow.mapSize.width = 2048;
        sunLight.shadow.mapSize.height = 2048;
        this.scene.add(sunLight);
    }
    
    /**
     * Define planet data with realistic proportions
     */
    setupPlanetData() {
        this.planetData = [
            {
                name: 'Mercury',
                radius: 0.8,
                distance: 15,
                speed: 0.02,
                color: 0x8c7853,
                info: 'Closest planet to the Sun. Extremely hot days, freezing nights.'
            },
            {
                name: 'Venus',
                radius: 1.2,
                distance: 22,
                speed: 0.015,
                color: 0xffc649,
                info: 'Hottest planet in our solar system due to greenhouse effect.'
            },
            {
                name: 'Earth',
                radius: 1.3,
                distance: 30,
                speed: 0.01,
                color: 0x6b93d6,
                info: 'Our home planet. The only known planet with life.'
            },
            {
                name: 'Mars',
                radius: 1.0,
                distance: 40,
                speed: 0.008,
                color: 0xcd5c5c,
                info: 'The Red Planet. Has the largest volcano in the solar system.'
            },
            {
                name: 'Jupiter',
                radius: 4.0,
                distance: 60,
                speed: 0.005,
                color: 0xd8ca9d,
                info: 'Largest planet. Has a Great Red Spot storm larger than Earth.'
            },
            {
                name: 'Saturn',
                radius: 3.5,
                distance: 80,
                speed: 0.004,
                color: 0xfab27b,
                info: 'Famous for its beautiful ring system made of ice and rock.'
            },
            {
                name: 'Uranus',
                radius: 2.5,
                distance: 100,
                speed: 0.003,
                color: 0x4fd0e7,
                info: 'Ice giant that rotates on its side. Has faint rings.'
            },
            {
                name: 'Neptune',
                radius: 2.4,
                distance: 120,
                speed: 0.002,
                color: 0x4b70dd,
                info: 'Windiest planet with speeds up to 2,100 km/h.'
            }
        ];
        
        // Initialize speed multipliers
        this.planetData.forEach(planet => {
            this.planetSpeedMultipliers[planet.name.toLowerCase()] = 1;
        });
    }

    /**
     * Create the sun and all planets
     */
    async createSolarSystem() {
        // Create the Sun
        this.createSun();

        // Create background stars
        this.createStars();

        // Create all planets
        this.planetData.forEach((planetData, index) => {
            this.createPlanet(planetData, index);
        });
    }

    /**
     * Create the Sun at the center
     */
    createSun() {
        const sunGeometry = new THREE.SphereGeometry(5, 32, 32);
        const sunMaterial = new THREE.MeshBasicMaterial({
            color: 0xffff00,
            emissive: 0xffaa00,
            emissiveIntensity: 0.3
        });

        this.sun = new THREE.Mesh(sunGeometry, sunMaterial);
        this.sun.name = 'Sun';
        this.sun.userData = {
            info: 'The Sun - Our star that provides light and heat to all planets.'
        };
        this.scene.add(this.sun);
    }

    /**
     * Create background stars
     */
    createStars() {
        const starGeometry = new THREE.BufferGeometry();
        const starCount = 1000;
        const positions = new Float32Array(starCount * 3);

        for (let i = 0; i < starCount * 3; i += 3) {
            positions[i] = (Math.random() - 0.5) * 2000;     // x
            positions[i + 1] = (Math.random() - 0.5) * 2000; // y
            positions[i + 2] = (Math.random() - 0.5) * 2000; // z
        }

        starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

        const starMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 2,
            sizeAttenuation: false
        });

        const stars = new THREE.Points(starGeometry, starMaterial);
        this.scene.add(stars);
        this.stars.push(stars);
    }

    /**
     * Create a planet with orbital mechanics
     */
    createPlanet(planetData, index) {
        // Create planet geometry and material
        const planetGeometry = new THREE.SphereGeometry(planetData.radius, 32, 32);
        const planetMaterial = new THREE.MeshLambertMaterial({
            color: planetData.color
        });

        const planet = new THREE.Mesh(planetGeometry, planetMaterial);
        planet.name = planetData.name;
        planet.userData = {
            info: planetData.info,
            distance: planetData.distance,
            speed: planetData.speed,
            angle: Math.random() * Math.PI * 2, // Random starting position
            originalSpeed: planetData.speed
        };

        // Position planet at its orbital distance
        planet.position.x = planetData.distance;
        planet.position.z = 0;

        // Enable shadows
        planet.castShadow = true;
        planet.receiveShadow = true;

        this.scene.add(planet);
        this.planets.push(planet);

        // Create orbital path visualization
        this.createOrbitPath(planetData.distance);
    }

    /**
     * Create orbital path rings
     */
    createOrbitPath(distance) {
        const orbitGeometry = new THREE.RingGeometry(distance - 0.1, distance + 0.1, 64);
        const orbitMaterial = new THREE.MeshBasicMaterial({
            color: 0x444444,
            side: THREE.DoubleSide,
            transparent: true,
            opacity: 0.1
        });

        const orbit = new THREE.Mesh(orbitGeometry, orbitMaterial);
        orbit.rotation.x = -Math.PI / 2;
        this.scene.add(orbit);
    }

    /**
     * Setup UI controls and event listeners
     */
    setupControls() {
        // Planet speed sliders
        this.planetData.forEach(planetData => {
            const slider = document.getElementById(`${planetData.name.toLowerCase()}-speed`);
            const valueDisplay = slider.nextElementSibling;

            slider.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                this.planetSpeedMultipliers[planetData.name.toLowerCase()] = value;
                valueDisplay.textContent = `${value.toFixed(1)}x`;
            });
        });

        // Global speed control
        const globalSpeedSlider = document.getElementById('globalSpeed');
        const globalValueDisplay = globalSpeedSlider.nextElementSibling;

        globalSpeedSlider.addEventListener('input', (e) => {
            this.globalSpeedMultiplier = parseFloat(e.target.value);
            globalValueDisplay.textContent = `${this.globalSpeedMultiplier.toFixed(1)}x`;
        });

        // Pause/Resume button
        const pauseBtn = document.getElementById('pauseBtn');
        pauseBtn.addEventListener('click', () => {
            this.togglePause();
        });

        // Reset button
        const resetBtn = document.getElementById('resetBtn');
        resetBtn.addEventListener('click', () => {
            this.resetAllSpeeds();
        });

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        themeToggle.addEventListener('click', () => {
            this.toggleTheme();
        });
    }

    /**
     * Setup event listeners for interaction
     */
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });

        // Mouse movement for planet hover
        this.canvas.addEventListener('mousemove', (event) => {
            this.onMouseMove(event);
        });

        // Mouse click for camera focus
        this.canvas.addEventListener('click', (event) => {
            this.onMouseClick(event);
        });
    }

    /**
     * Handle window resize
     */
    onWindowResize() {
        const container = this.canvas.parentElement;
        const width = container.clientWidth;
        const height = container.clientHeight;

        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }

    /**
     * Handle mouse movement for planet hover
     */
    onMouseMove(event) {
        const rect = this.canvas.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        this.checkPlanetHover();
    }

    /**
     * Handle mouse click for camera interaction
     */
    onMouseClick(event) {
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects([this.sun, ...this.planets]);

        if (intersects.length > 0) {
            const clickedObject = intersects[0].object;
            this.focusOnObject(clickedObject);
        }
    }

    /**
     * Check for planet hover and show info
     */
    checkPlanetHover() {
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects([this.sun, ...this.planets]);

        if (intersects.length > 0) {
            const hoveredObject = intersects[0].object;
            if (hoveredObject !== this.hoveredPlanet) {
                this.hoveredPlanet = hoveredObject;
                this.showPlanetInfo(hoveredObject);
            }
        } else {
            if (this.hoveredPlanet) {
                this.hoveredPlanet = null;
                this.hidePlanetInfo();
            }
        }
    }

    /**
     * Show planet information tooltip
     */
    showPlanetInfo(planet) {
        const planetName = document.getElementById('planetName');
        const planetDetails = document.getElementById('planetDetails');

        planetName.textContent = planet.name;
        planetDetails.textContent = planet.userData.info;

        this.planetInfo.classList.remove('hidden');
    }

    /**
     * Hide planet information tooltip
     */
    hidePlanetInfo() {
        this.planetInfo.classList.add('hidden');
    }

    /**
     * Focus camera on a specific object
     */
    focusOnObject(object) {
        const targetPosition = object.position.clone();
        const distance = object.name === 'Sun' ? 30 : 15;

        // Calculate camera position
        const cameraPosition = targetPosition.clone();
        cameraPosition.z += distance;
        cameraPosition.y += distance * 0.3;

        // Smooth camera transition (simplified)
        this.camera.position.copy(cameraPosition);
        this.camera.lookAt(targetPosition);
    }

    /**
     * Toggle pause/resume animation
     */
    togglePause() {
        this.isPaused = !this.isPaused;
        const pauseBtn = document.getElementById('pauseBtn');
        pauseBtn.textContent = this.isPaused ? '▶️ Resume' : '⏸️ Pause';
    }

    /**
     * Reset all planet speeds to default
     */
    resetAllSpeeds() {
        // Reset planet speed multipliers
        Object.keys(this.planetSpeedMultipliers).forEach(key => {
            this.planetSpeedMultipliers[key] = 1;
        });

        // Reset global speed
        this.globalSpeedMultiplier = 1;

        // Update UI sliders
        document.querySelectorAll('.speed-slider').forEach(slider => {
            slider.value = 1;
            slider.nextElementSibling.textContent = '1.0x';
        });
    }

    /**
     * Toggle between light and dark theme
     */
    toggleTheme() {
        document.body.classList.toggle('light-theme');
        const themeToggle = document.getElementById('themeToggle');
        const isLight = document.body.classList.contains('light-theme');
        themeToggle.textContent = isLight ? '🌙 Dark' : '☀️ Light';

        // Update scene background
        this.scene.background = new THREE.Color(isLight ? 0xf0f0f0 : 0x000011);
    }

    /**
     * Start the animation loop
     */
    startAnimation() {
        this.animate();
    }

    /**
     * Main animation loop
     */
    animate() {
        requestAnimationFrame(() => this.animate());

        if (!this.isPaused) {
            this.updatePlanets();
            this.updateSun();
        }

        this.renderer.render(this.scene, this.camera);
    }

    /**
     * Update planet positions and rotations
     */
    updatePlanets() {
        const deltaTime = this.clock.getDelta();

        this.planets.forEach((planet, index) => {
            const planetData = this.planetData[index];
            const speedMultiplier = this.planetSpeedMultipliers[planetData.name.toLowerCase()];

            // Update orbital angle
            planet.userData.angle += planetData.speed * speedMultiplier * this.globalSpeedMultiplier * deltaTime * 10;

            // Update position based on orbital mechanics
            planet.position.x = Math.cos(planet.userData.angle) * planet.userData.distance;
            planet.position.z = Math.sin(planet.userData.angle) * planet.userData.distance;

            // Rotate planet on its axis
            planet.rotation.y += 0.01 * speedMultiplier * this.globalSpeedMultiplier;
        });
    }

    /**
     * Update sun rotation
     */
    updateSun() {
        if (this.sun) {
            this.sun.rotation.y += 0.005;
        }
    }

    /**
     * Hide loading screen when everything is ready
     */
    hideLoadingScreen() {
        setTimeout(() => {
            this.loadingScreen.style.opacity = '0';
            setTimeout(() => {
                this.loadingScreen.style.display = 'none';
            }, 500);
        }, 1000);
    }
}

// Initialize the solar system when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SolarSystem();
});

// Handle page visibility changes to pause/resume when tab is not active
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Page is hidden, could pause animation for performance
    } else {
        // Page is visible again
    }
});
