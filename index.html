<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Solar System Simulation</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>3D Solar System Simulation</h1>
            <div class="header-controls">
                <button id="pauseBtn" class="control-btn">⏸️ Pause</button>
                <button id="themeToggle" class="control-btn">🌙 Dark</button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- 3D Canvas -->
            <div class="canvas-container">
                <canvas id="solarSystemCanvas"></canvas>
                <div id="planetInfo" class="planet-info hidden">
                    <h3 id="planetName"></h3>
                    <p id="planetDetails"></p>
                </div>
            </div>

            <!-- Control Panel -->
            <aside class="control-panel">
                <h2>Orbital Speed Controls</h2>
                <div class="controls-grid">
                    <!-- Mercury -->
                    <div class="planet-control">
                        <label for="mercury-speed">
                            <span class="planet-icon mercury"></span>
                            Mercury
                        </label>
                        <input type="range" id="mercury-speed" class="speed-slider" 
                               min="0" max="5" step="0.1" value="1">
                        <span class="speed-value">1.0x</span>
                    </div>

                    <!-- Venus -->
                    <div class="planet-control">
                        <label for="venus-speed">
                            <span class="planet-icon venus"></span>
                            Venus
                        </label>
                        <input type="range" id="venus-speed" class="speed-slider" 
                               min="0" max="5" step="0.1" value="1">
                        <span class="speed-value">1.0x</span>
                    </div>

                    <!-- Earth -->
                    <div class="planet-control">
                        <label for="earth-speed">
                            <span class="planet-icon earth"></span>
                            Earth
                        </label>
                        <input type="range" id="earth-speed" class="speed-slider" 
                               min="0" max="5" step="0.1" value="1">
                        <span class="speed-value">1.0x</span>
                    </div>

                    <!-- Mars -->
                    <div class="planet-control">
                        <label for="mars-speed">
                            <span class="planet-icon mars"></span>
                            Mars
                        </label>
                        <input type="range" id="mars-speed" class="speed-slider" 
                               min="0" max="5" step="0.1" value="1">
                        <span class="speed-value">1.0x</span>
                    </div>

                    <!-- Jupiter -->
                    <div class="planet-control">
                        <label for="jupiter-speed">
                            <span class="planet-icon jupiter"></span>
                            Jupiter
                        </label>
                        <input type="range" id="jupiter-speed" class="speed-slider" 
                               min="0" max="5" step="0.1" value="1">
                        <span class="speed-value">1.0x</span>
                    </div>

                    <!-- Saturn -->
                    <div class="planet-control">
                        <label for="saturn-speed">
                            <span class="planet-icon saturn"></span>
                            Saturn
                        </label>
                        <input type="range" id="saturn-speed" class="speed-slider" 
                               min="0" max="5" step="0.1" value="1">
                        <span class="speed-value">1.0x</span>
                    </div>

                    <!-- Uranus -->
                    <div class="planet-control">
                        <label for="uranus-speed">
                            <span class="planet-icon uranus"></span>
                            Uranus
                        </label>
                        <input type="range" id="uranus-speed" class="speed-slider" 
                               min="0" max="5" step="0.1" value="1">
                        <span class="speed-value">1.0x</span>
                    </div>

                    <!-- Neptune -->
                    <div class="planet-control">
                        <label for="neptune-speed">
                            <span class="planet-icon neptune"></span>
                            Neptune
                        </label>
                        <input type="range" id="neptune-speed" class="speed-slider" 
                               min="0" max="5" step="0.1" value="1">
                        <span class="speed-value">1.0x</span>
                    </div>
                </div>

                <!-- Global Controls -->
                <div class="global-controls">
                    <h3>Global Controls</h3>
                    <div class="control-group">
                        <label for="globalSpeed">Overall Speed:</label>
                        <input type="range" id="globalSpeed" class="speed-slider" 
                               min="0" max="3" step="0.1" value="1">
                        <span class="speed-value">1.0x</span>
                    </div>
                    <div class="control-group">
                        <button id="resetBtn" class="control-btn">🔄 Reset All</button>
                    </div>
                </div>
            </aside>
        </main>

        <!-- Loading Screen -->
        <div id="loadingScreen" class="loading-screen">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <h2>Loading Solar System...</h2>
                <p>Initializing 3D simulation</p>
            </div>
        </div>
    </div>

    <!-- Three.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- Main Application Script -->
    <script src="script.js"></script>
</body>
</html>
