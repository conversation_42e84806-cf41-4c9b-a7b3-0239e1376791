/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark Theme Colors */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --accent-primary: #4a9eff;
    --accent-secondary: #ff6b35;
    --border-color: #333333;
    --shadow-color: rgba(0, 0, 0, 0.5);
    
    /* Planet Colors */
    --mercury-color: #8c7853;
    --venus-color: #ffc649;
    --earth-color: #6b93d6;
    --mars-color: #cd5c5c;
    --jupiter-color: #d8ca9d;
    --saturn-color: #fab27b;
    --uranus-color: #4fd0e7;
    --neptune-color: #4b70dd;
}

/* Light Theme */
body.light-theme {
    --bg-primary: #f5f5f5;
    --bg-secondary: #ffffff;
    --bg-tertiary: #e0e0e0;
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #dddddd;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Orbitron', monospace;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow-x: hidden;
    min-height: 100vh;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-bottom: 2px solid var(--border-color);
    box-shadow: 0 2px 10px var(--shadow-color);
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--accent-primary);
    text-shadow: 0 0 10px rgba(74, 158, 255, 0.3);
}

.header-controls {
    display: flex;
    gap: 1rem;
}

.control-btn {
    padding: 0.5rem 1rem;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    cursor: pointer;
    font-family: inherit;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: var(--accent-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

/* Main Content Layout */
.main-content {
    display: flex;
    flex: 1;
    gap: 1rem;
    padding: 1rem;
}

/* Canvas Container */
.canvas-container {
    flex: 1;
    position: relative;
    background: var(--bg-secondary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 20px var(--shadow-color);
}

#solarSystemCanvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* Planet Info Tooltip */
.planet-info {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 250px;
    z-index: 10;
}

.planet-info.hidden {
    display: none;
}

.planet-info h3 {
    margin-bottom: 0.5rem;
    color: var(--accent-primary);
}

/* Control Panel */
.control-panel {
    width: 350px;
    background: var(--bg-secondary);
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px var(--shadow-color);
    max-height: calc(100vh - 140px);
    overflow-y: auto;
}

.control-panel h2 {
    margin-bottom: 1.5rem;
    color: var(--accent-primary);
    text-align: center;
    font-size: 1.3rem;
}

.controls-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

/* Planet Control Styles */
.planet-control {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.planet-control label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 0.9rem;
}

.planet-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-block;
}

.planet-icon.mercury { background: var(--mercury-color); }
.planet-icon.venus { background: var(--venus-color); }
.planet-icon.earth { background: var(--earth-color); }
.planet-icon.mars { background: var(--mars-color); }
.planet-icon.jupiter { background: var(--jupiter-color); }
.planet-icon.saturn { background: var(--saturn-color); }
.planet-icon.uranus { background: var(--uranus-color); }
.planet-icon.neptune { background: var(--neptune-color); }

/* Slider Styles */
.speed-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--bg-primary);
    outline: none;
    -webkit-appearance: none;
}

.speed-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--accent-primary);
    cursor: pointer;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.speed-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--accent-primary);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.speed-value {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
    font-weight: 700;
}

/* Global Controls */
.global-controls {
    border-top: 2px solid var(--border-color);
    padding-top: 1.5rem;
}

.global-controls h3 {
    margin-bottom: 1rem;
    color: var(--accent-secondary);
    text-align: center;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.control-group label {
    font-weight: 700;
    font-size: 0.9rem;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        flex-direction: column;
    }
    
    .control-panel {
        width: 100%;
        max-height: none;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-controls {
        justify-content: center;
    }
    
    .main-content {
        padding: 0.5rem;
    }
    
    .control-panel {
        padding: 1rem;
    }
    
    .controls-grid {
        gap: 0.8rem;
    }
    
    .planet-control {
        padding: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.2rem;
    }
    
    .control-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
    
    .planet-info {
        max-width: 200px;
        padding: 0.8rem;
    }
}

/* Scrollbar Styling */
.control-panel::-webkit-scrollbar {
    width: 6px;
}

.control-panel::-webkit-scrollbar-track {
    background: var(--bg-primary);
    border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
}
